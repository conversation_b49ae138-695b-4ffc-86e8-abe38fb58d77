 Export Manager: Initializing
 Export Manager: Looking for button container... 
 Export Manager: <PERSON><PERSON> inserted before Clear All button
 Export Manager: <PERSON><PERSON> created and added to UI
 Export Manager: Checking for latest report...
 Export Manager: Debug functions available at window.debugExportManager
 Environment Management loaded
 DOM content loaded. Initializing App and modules...
 ElementInteractions module should be loaded globally.
 [AppiumAutomationApp CONSTRUCTOR] New instance created with ID: 1750581385715_0.5135040234385255
 Generated client session ID: client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5
 MultiStepAction instantiated in App constructor.
 RepeatStepsAction instantiated in App constructor.
 HookAction instantiated in App constructor.
 Add tap fallback locator button not found
initEventListeners @ fallback-locators.js:27
 FallbackLocatorsManager initialized
 FallbackLocatorsManager instantiated in App constructor.
 TapFallbackManager initialized
 TapFallbackManager instantiated in App constructor.
 Initializing Appium Automation App...
 Initializing Sortable on actionsList element
 Sortable initialized successfully
 Event listener for retry/remove on actionsList attached.
 Loading reference images for action type: tap, target: tapImageFilename
 Loading reference images for action type: tap, target: general
 Loading reference images for action type: doubleTap, target: general
 Loading reference images for action type: clickImageAirtest, target: general
 Loading reference images for action type: waitImageAirtest, target: general
 Loading reference images for action type: doubleClickImageAirtest, target: general
 Appium Automation App initialized
 ElementInteractions initialized successfully
 TestCaseManager module should be loaded globally.
 TestCaseManager initialized successfully
 Setting up TestCaseManager dependent event listeners...
 TestCaseManager listeners setup complete.
 ExecutionManager: Event listener added to Execute All button.
 ExecutionManager: Event listener added to Stop button.
 ExecutionManager initialized successfully
 ActionManager initialized successfully
 Settings manager initialized
 Loaded 22 random data generators
 Loaded 22 random data generators
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: true
   - nowDisabled: false
   - button element: 
 Reference images: Object
 Added 76 reference images to select
 Global values loaded: Object
 Reference images: Object
 Added 76 reference images to select
 Current environment response: Object
 Setting active environment from server: 5
 Populating dropdown with active environment: 5
 Selecting environment UI: 5 Current active: 5
 Reference images: Object
 Added 76 reference images to select
 Reference images: Object
 Added 76 reference images to select
 Reference images: Object
 Added 76 reference images to select
 Reference images: Object
 Added 76 reference images to select
 Auto-refreshing device list on page load...
 [info] Refreshing device list...
 [success] Found 1 device(s)
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Quick check 1/12 for reports
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Attempting to connect to device: 00008120-00186C801E13C01E, platform: iOS
 [info] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Quick check 2/12 for reports
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Refreshing device screen after connection
 [info] Device info updated: 00008120-00186C801E13C01E
 Device dimensions from connect response: 393x852
 [success] Connected to device: 00008120-00186C801E13C01E with AirTest support
 Starting test suite loading
 Starting test suite loading
 Got test suite data: Object
 Test Suite: TC3, Test Cases: Array(2)
 Test Suite: TC1, Test Cases: Array(2)
 Test Suite: KMART AU PROD_Copy_20250604182837, Test Cases: Array(10)
 Test Suite: temp test, Test Cases: Array(2)
 Test Suite: KMART NZ PROD, Test Cases: Array(9)
 Test Suite: Database Test, Test Cases: Array(1)
 Test Suite: just, Test Cases: Array(1)
 Test Suite: andriod, Test Cases: Array(1)
 Test Suite: TC2, Test Cases: Array(2)
 Test Suite: KMART AU PROD, Test Cases: Array(11)
 Got test suite data: Object
 Test Suite: TC3, Test Cases: Array(2)
 Test Suite: TC1, Test Cases: Array(2)
 Test Suite: KMART AU PROD_Copy_20250604182837, Test Cases: Array(10)
 Test Suite: temp test, Test Cases: Array(2)
 Test Suite: KMART NZ PROD, Test Cases: Array(9)
 Test Suite: Database Test, Test Cases: Array(1)
 Test Suite: just, Test Cases: Array(1)
 Test Suite: andriod, Test Cases: Array(1)
 Test Suite: TC2, Test Cases: Array(2)
 Test Suite: KMART AU PROD, Test Cases: Array(11)
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581397680
 [success] Screenshot refreshed
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Selected test suite: TC3 (2bd4bbfb-cd4c-46ca-bb71-a6fcf75036ca)
 Cleared currentActions array
 Cleared actionsList UI
 Reset test case name and filename
 Set isTestSuiteLoaded = false (cleared)
 [AppiumAutomationApp updateExecutionButtons Instance: 1750581385715_0.5135040234385255] Checking actions array:  Object
 Save button disabled: true
 Save As button disabled: true
 Updated execution buttons. Has actions: false, Has action items: false, Is connected: true
 [info] Cleaning up screenshots...
 [info] All actions cleared
 [success] All screenshots deleted successfully
 Test suite details: Object
 Processing 2 unique test cases of 2 total
 Loading test case with filename: health2_20250408214926.json
 Loaded test case data for health2_20250408214926.json: Object
 Loading test case with filename: apple_health_Copy_20250409201903.json
 Loaded test case data for apple_health_Copy_20250409201903.json: Object
 Adding 2 test cases after name deduplication
 Setting test case test-case-health2-20250408214926-json with index 0
 Setting test case test-case-apple-health-Copy-20250409201903-json with index 1
 [AppiumAutomationApp updateExecutionButtons Instance: 1750581385715_0.5135040234385255] Checking actions array:  Object
 Save button disabled: false
 Save As button disabled: false
 Updated execution buttons. Has actions: true, Has action items: true, Is connected: true
 Set isTestSuiteLoaded = true
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Quick check 3/12 for reports
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_182802
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 ExecutionManager: executeAllActions called
 [ExecutionManager executeAllActions App Instance ID: 1750581385715_0.5135040234385255] In executeAllActions: this.app.currentActions is: Array(9) Length: 9
 [info] Initializing report directory and screenshots folder for test suite...
 [success] Report directory initialized successfully
 [info] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250622_183643
 [info] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250622_183643/screenshots
 [info] Deleting all screenshots in app/static/screenshots folder...
 [success] All screenshots deleted successfully
 [info] Clearing screenshots from database before execution...
 [success] Cleared 1 screenshots from database
 Initialized internal test case states: Array(2)
 Export Manager: Execution started event received
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: true
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: false
   - isExecuting: true
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: false
   - nowDisabled: true
   - button element: 
 Started periodic Appium session health checks
 [info] ExecutionManager: Starting execution of 9 actions...
 [info] Executing action 1/9: Launch app: com.apple.Health
 [Action Status] ee5KkVz90e=running
 Marked test case test-case-health2-20250408214926-json as current with test_idx=0
 Updating TC State for testIdx 0: Old Status: pending, New Status: running, Error: null
 Will use test_idx=0 for actions in this test case
 Added test_idx=0 to action data for execution
 [Action Status] ee5KkVz90e=pass
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581406368
 [success] Screenshot refreshed
 Fixed screen observer: Updating screenshot src from http://localhost:8080/screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581406368
 [info] Executing action 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
 [Action Status] E5An5BbVuK=running
 Added test_idx=0 to action data for execution
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [Action Status] E5An5BbVuK=pass
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581409634
 [success] Screenshot refreshed
 Fixed screen observer: Updating screenshot src from http://localhost:8080/screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581409634
 [info] Executing action 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
 [Action Status] KfOSdvcOkk=running
 Added test_idx=0 to action data for execution
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [error] Action 3 failed: Element with xpath ' //XCUIElementTypeButton[@name="Done"]' not clickable within timeout of 10 seconds
 [Action Status] KfOSdvcOkk=fail
 Finding hook actions for test case: test-case-health2-20250408214926-json
 Found 0 hook actions in test case test-case-health2-20250408214926-json
 _handleTestCaseFailure: Explicitly setting testIdx 0 to 'failed'
 Updating TC State for testIdx 0: Old Status: running, New Status: failed, Error: Failed at action index 2
 [info] Skipping remaining steps in failed test case (moving from action 3 to next test case at 5)
 [info] Executing action 6/9: Launch app: com.apple.Health
 [Action Status] wp1dY1wJ58=running
 Marked test case test-case-apple-health-Copy-20250409201903-json as current with test_idx=1
 Updating TC State for testIdx 1: Old Status: pending, New Status: running, Error: null
 Will use test_idx=1 for actions in this test case
 Detected new test case at index 5. Previous test case: test-case-health2-20250408214926-json, Current test case: test-case-apple-health-Copy-20250409201903-json
 Adding a 10-second delay before starting a new test case at index 5
 Delay completed, continuing with test case at index 5
 Added test_idx=1 to action data for execution
 Performing periodic Appium session health check
 [Action Status] wp1dY1wJ58=pass
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581433530
 [success] Screenshot refreshed
 Fixed screen observer: Updating screenshot src from http://localhost:8080/screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581433530
 [info] Executing action 7/9: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
 [Action Status] HphRLWPfSD=running
 Added test_idx=1 to action data for execution
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [Action Status] HphRLWPfSD=pass
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581436405
 [success] Screenshot refreshed
 Fixed screen observer: Updating screenshot src from http://localhost:8080/screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581436405
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [info] Executing action 8/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
 [Action Status] AoLct5ZYWj=running
 Added test_idx=1 to action data for execution
 [Action Status] AoLct5ZYWj=pass
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581440064
 [success] Screenshot refreshed
 Fixed screen observer: Updating screenshot src from http://localhost:8080/screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581440064
 [info] Executing action 9/9: Terminate app: com.apple.Health
 [Action Status] mOoxO3pBlm=running
 Added test_idx=1 to action data for execution
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 [Action Status] mOoxO3pBlm=pass
 Refreshing screenshot for device: 00008120-00186C801E13C01E
 [info] Refreshing screenshot...
 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581443845
 [success] Screenshot refreshed
 Fixed screen observer: Updating screenshot src from http://localhost:8080/screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581443845
 Test case states BEFORE finalization: Array(2)
 Test case 0 has UI errors, marking as FAILED
 Updating TC State for testIdx 0: Old Status: failed, New Status: failed, Error: UI elements indicate failure
 Test case 1 finalized from 'running' to 'passed'
 Updating TC State for testIdx 1: Old Status: running, New Status: passed, Error: null
 Test case states AFTER finalization: Array(2)
 [executeAllActions] Test Case States: Array(2)
 [executeAllActions] lastExecutionHadFailures: true
 [warning] 1 test failed.
 1 tests failed (from internal state)
 [_updateUIForStop] Test Case States before Rerun Button Check: Array(2)
 [_updateUIForStop] lastExecutionHadFailures: true
 Stopped periodic Appium session health checks
 Export Manager: Execution completed event received
 Export Manager: Setting isExecuting to false and checking for latest report
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_182802
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_182802
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_182802
   - wasDisabled: true
   - nowDisabled: false
   - button element: 
 [info] Generating execution report...
 [info] Saving 67 action log entries to file...
 [error] Execution failed but report was generated.
 [success] Action logs saved successfully
 Test case "health2" collapsed
 Test case "apple health (Copy)" collapsed
 [success] Execution report generated successfully
 [info] Cleaning up screenshots...
 [success] All screenshots deleted successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Screenshot loaded successfully
 [success] Screenshot refreshed successfully
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Quick check 12/12 for reports
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: First check (2s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 ExecutionManager: Dispatching execution-completed event after report generation
 Export Manager: Execution completed event received
 Export Manager: Setting isExecuting to false and checking for latest report
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: First check (2s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Second check (5s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Third check (5s) - checking for latest report...
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Latest report response: Object
 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:48 Export Manager: Second check (5s) - checking for latest report...
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:58 Export Manager: Third check (5s) - checking for latest report...
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:53 Export Manager: Third check (8s) - checking for latest report...
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:53 Export Manager: Third check (8s) - checking for latest report...
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: Object
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
main.js:492 Retry button clicked for test case: health2 (File: health2_20250408214926.json)
main.js:2360 [info] Executing action: Launch app: com.apple.Health
main.js:4495 Test case "health2" expanded
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
main.js:1868 Refreshing screenshot for device: 00008120-00186C801E13C01E
main.js:2360 [info] Refreshing screenshot...
main.js:1875 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581465959
main.js:2360 [success] Screenshot refreshed
main.js:2360 [success] Action executed: Action executed successfully: launchApp
main.js:2406 [Action Status] ee5KkVz90e=pass
main.js:2360 [info] Executing action: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
main.js:1895 Screenshot loaded successfully
main.js:2360 [success] Screenshot refreshed successfully
main.js:1868 Refreshing screenshot for device: 00008120-00186C801E13C01E
main.js:2360 [info] Refreshing screenshot...
main.js:1875 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581468775
main.js:2360 [success] Screenshot refreshed
main.js:2360 [success] Action executed: Action executed successfully: clickElement
main.js:2406 [Action Status] E5An5BbVuK=pass
main.js:2360 [info] Executing action: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
main.js:1895 Screenshot loaded successfully
main.js:2360 [success] Screenshot refreshed successfully
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
main.js:1868 Refreshing screenshot for device: 00008120-00186C801E13C01E
main.js:2360 [info] Refreshing screenshot...
main.js:1875 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581471978
main.js:2360 [success] Screenshot refreshed
main.js:2360 [success] Action executed: Action executed successfully: clickElement
main.js:2406 [Action Status] KfOSdvcOkk=pass
main.js:2360 [info] Executing action: Wait for 1 ms
main.js:1895 Screenshot loaded successfully
main.js:2360 [success] Screenshot refreshed successfully
main.js:1868 Refreshing screenshot for device: 00008120-00186C801E13C01E
main.js:2360 [info] Refreshing screenshot...
main.js:1875 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581474567
main.js:2360 [success] Screenshot refreshed
main.js:2360 [success] Action executed: Action executed successfully: wait
main.js:2406 [Action Status] 4kBvNvFi5i=pass
main.js:2360 [info] Executing action: Terminate app: com.apple.Health
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
main.js:1895 Screenshot loaded successfully
main.js:2360 [success] Screenshot refreshed successfully
main.js:1868 Refreshing screenshot for device: 00008120-00186C801E13C01E
main.js:2360 [info] Refreshing screenshot...
main.js:1875 Refreshing screenshot with session-aware URL: /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750581385715_rjq1jm03b_1750580861270_5pdkzx3e5&t=1750581477994
main.js:2360 [success] Screenshot refreshed
main.js:2360 [success] Action executed: Action executed successfully: terminateApp
main.js:2406 [Action Status] yvWe991wY2=pass
main.js:1895 Screenshot loaded successfully
main.js:2360 [success] Screenshot refreshed successfully
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:234 Export response: {download_url: '/api/reports/download_export/export_testsuite_execution_20250622_183643_20250622_183812.zip', filename: 'export_testsuite_execution_20250622_183643_20250622_183812.zip', message: 'Report generated successfully', success: true}
export-run.js:255 Download attempt 1...
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
export-run.js:69 Export Manager: Periodic check for reports (5s interval)
export-run.js:130 Export Manager: Checking for latest report...
export-run.js:134 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
export-run.js:135 Export Manager: Response status: 200
export-run.js:144 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
export-run.js:181 Export Manager: updateExportButtonState called
export-run.js:182 Export Manager: exportButton exists: true
export-run.js:183 Export Manager: isExecuting: false
export-run.js:184 Export Manager: latestReportId: testsuite_execution_20250622_183643
export-run.js:196 Export Manager: Button state updated:
export-run.js:197   - shouldEnable: testsuite_execution_20250622_183643
export-run.js:198   - isExecuting: false
export-run.js:199   - latestReportId: testsuite_execution_20250622_183643
export-run.js:200   - wasDisabled: false
export-run.js:201   - nowDisabled: false
export-run.js:202   - button element: <button id=​"exportRunBtn" class=​"btn me-2 btn-info">​…​</button>​flex
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
 Export Manager: Periodic check for reports (5s interval)
 Export Manager: Checking for latest report...
 Export Manager: Latest report response: {report: {…}, report_url: '/reports/testsuite_execution_20250622_183643/mainreport.html', status: 'success', success: true}
 Export Manager: Response status: 200
 Export Manager: Found report ID from data.report.report_id: testsuite_execution_20250622_183643
 Export Manager: updateExportButtonState called
 Export Manager: exportButton exists: true
 Export Manager: isExecuting: false
 Export Manager: latestReportId: testsuite_execution_20250622_183643
 Export Manager: Button state updated:
   - shouldEnable: testsuite_execution_20250622_183643
   - isExecuting: false
   - latestReportId: testsuite_execution_20250622_183643
   - wasDisabled: false
   - nowDisabled: false
   - button element: 
